"""
大语言模型服务 - 直接透传API响应
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List, AsyncGenerator
from dataclasses import dataclass

from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class LLMRequest:
    """LLM请求数据结构"""
    messages: List[Dict[str, str]]
    model: Optional[str] = None
    max_tokens: int = 1000
    temperature: float = 0.7
    stream: bool = False
    tools: Optional[List[Dict[str, Any]]] = None


class LLMClient:
    """优化的LLM客户端"""

    def __init__(self, api_url: str, default_model: str = "ht::saas-deepseek-v3"):
        self.api_url = api_url
        self.default_model = default_model
        self.session = None
        # 为SSE流式响应设置更合理的超时时间
        self._timeout = aiohttp.ClientTimeout(
            total=300,      # 总超时5分钟（SSE需要更长）
            connect=30,     # 连接超时30秒
            sock_read=120   # 读取超时2分钟
        )
        # 优化连接器配置以支持更好的并发
        self._connector_config = {
            'limit': 100,           # 总连接池大小
            'limit_per_host': 20,   # 每个主机的连接数限制
            'ttl_dns_cache': 300,   # DNS缓存TTL
            'use_dns_cache': True,
            'keepalive_timeout': 60,  # Keep-alive超时
            'enable_cleanup_closed': True
        }

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话 - 优化并发性能"""
        if self.session is None or self.session.closed:
            # 创建优化的连接器
            connector = aiohttp.TCPConnector(**self._connector_config)
            self.session = aiohttp.ClientSession(
                timeout=self._timeout,
                connector=connector
            )
        return self.session

    def _build_payload(self, request: LLMRequest) -> Dict[str, Any]:
        """构建请求载荷"""
        payload = {
            "model": request.model or self.default_model,
            "messages": request.messages,
            "stream": request.stream,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature
        }
        
        # 如果有工具参数，添加到载荷中
        if request.tools:
            payload["tools"] = request.tools
            
        return payload

    async def _handle_response(self, response: aiohttp.ClientResponse, model: str) -> Any:
        """完全透明的API响应处理 - 原样返回任何格式"""
        try:
            # 尝试解析为JSON
            return await response.json()
        except json.JSONDecodeError:
            # 如果不是JSON，返回原始文本
            return await response.text()

    async def chat_completion(self, request: LLMRequest) -> Any:
        """聊天补全 - 完全透传LLM API响应和错误"""
        session = await self._get_session()
        payload = self._build_payload(request)
        model = payload["model"]

        try:
            async with session.post(
                self.api_url,
                headers={"Content-Type": "application/json"},
                json=payload
            ) as response:
                return await self._handle_response(response, model)
        except Exception as e:
            logger.error(f"LLM请求异常: {e}", exc_info=True)
            raise

    async def stream_chat_completion(self, request: LLMRequest) -> AsyncGenerator[str, None]:
        """流式聊天补全 - 改进的SSE流处理，支持重试和完整性检查"""
        session = await self._get_session()
        payload = self._build_payload(request)
        payload["stream"] = True

        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                async with session.post(
                    self.api_url,
                    headers={"Content-Type": "application/json"},
                    json=payload
                ) as response:
                    # 检查响应状态
                    if response.status != 200:
                        logger.warning(f"LLM API返回非200状态码: {response.status}")
                        if retry_count < max_retries:
                            retry_count += 1
                            await asyncio.sleep(0.5 * retry_count)  # 指数退避
                            continue
                        else:
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status
                            )

                    # 流式数据处理，增加完整性检查
                    received_data = False
                    last_chunk_time = asyncio.get_event_loop().time()

                    async for line in response.content:
                        current_time = asyncio.get_event_loop().time()
                        line_str = line.decode('utf-8').strip()

                        if line_str:
                            received_data = True
                            last_chunk_time = current_time
                            # 完全原样返回SSE行 - 保持原有透传逻辑
                            yield line_str

                        # 检查是否长时间没有数据（可能连接断开）
                        elif current_time - last_chunk_time > 30:  # 30秒无数据
                            logger.warning("流式连接长时间无数据，可能已断开")
                            break

                    # 如果成功接收到数据，退出重试循环
                    if received_data:
                        break
                    else:
                        logger.warning("未接收到任何流式数据")
                        if retry_count < max_retries:
                            retry_count += 1
                            await asyncio.sleep(0.5 * retry_count)
                            continue

            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                logger.error(f"流式请求异常 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
                if retry_count < max_retries:
                    retry_count += 1
                    await asyncio.sleep(0.5 * retry_count)  # 指数退避
                    continue
                else:
                    raise
            except Exception as e:
                logger.error(f"流式请求未知异常: {e}", exc_info=True)
                raise

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

class LLMService:
    """大语言模型服务管理器"""

    def __init__(self):
        self.client = LLMClient(
            api_url=settings.custom_llm_api_url,
            default_model=settings.default_llm_model
        )
        logger.info(f"初始化LLM客户端: {settings.custom_llm_api_url}")

    async def chat_completion(self, messages: List[Dict[str, str]],
                            model: Optional[str] = None,
                            max_tokens: int = 1000,
                            temperature: float = 0.7,
                            tools: Optional[List[Dict[str, Any]]] = None) -> Any:
        """聊天补全 - 直接返回原始API响应"""
        request = LLMRequest(
            messages=messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=False,
            tools=tools
        )
        return await self.client.chat_completion(request)

    async def stream_chat_completion(self, messages: List[Dict[str, str]],
                                   model: Optional[str] = None,
                                   max_tokens: int = 1000,
                                   temperature: float = 0.7,
                                   tools: Optional[List[Dict[str, Any]]] = None) -> AsyncGenerator[str, None]:
        """流式聊天补全 - 完全原始SSE流"""
        request = LLMRequest(
            messages=messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=True,
            tools=tools
        )
        async for chunk in self.client.stream_chat_completion(request):
            yield chunk

    def list_available_models(self) -> List[str]:
        """列出所有可用的模型"""
        return settings.available_models

    def get_default_model(self) -> str:
        """获取默认模型"""
        return settings.default_llm_model

    def validate_model(self, model: str) -> bool:
        """验证模型是否可用"""
        return model in settings.available_models

    async def close(self):
        """关闭服务"""
        await self.client.close()


# 全局LLM服务实例
llm_service = LLMService()
